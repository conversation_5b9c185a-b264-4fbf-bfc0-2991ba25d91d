import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard_models.dart';
import 'resizable_chart_card.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// مدير تخطيط لوحة التحكم المتقدم
class DashboardLayoutManager extends StatefulWidget {
  final List<ChartCardModel> cards;
  final Function(List<ChartCardModel>)? onCardsReordered;
  final Function(ChartCardModel)? onCardUpdated;
  final VoidCallback? onRefresh;

  const DashboardLayoutManager({
    super.key,
    required this.cards,
    this.onCardsReordered,
    this.onCardUpdated,
    this.onRefresh,
  });

  @override
  State<DashboardLayoutManager> createState() => _DashboardLayoutManagerState();
}

class _DashboardLayoutManagerState extends State<DashboardLayoutManager>
    with TickerProviderStateMixin {
  late List<ChartCardModel> _cards;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _columns = 2;
  double _spacing = 16.0;
  bool _isEditMode = false;

  // أوضاع العرض الثلاثة
  int _viewMode = 0; // 0: شبكي، 1: قائمة، 2: قابل للتخصيص

  @override
  void initState() {
    super.initState();
    _cards = List.from(widget.cards);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    // تحميل تفضيلات التخطيط المحفوظة
    _loadLayoutPreferences();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل تفضيلات التخطيط المحفوظة
  Future<void> _loadLayoutPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      setState(() {
        _columns = prefs.getInt('dashboard_layout_columns') ?? 2;
        _spacing = prefs.getDouble('dashboard_layout_spacing') ?? 16.0;
        _viewMode = prefs.getInt('dashboard_layout_view_mode') ?? 0;
      });

      debugPrint('✅ تم تحميل تفضيلات التخطيط:');
      debugPrint('   الأعمدة: $_columns');
      debugPrint('   التباعد: $_spacing');
      debugPrint('   وضع العرض: $_viewMode (0:شبكي، 1:قائمة، 2:مخصص)');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل تفضيلات التخطيط: $e');
    }
  }

  /// حفظ تفضيلات التخطيط
  Future<void> _saveLayoutPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setInt('dashboard_layout_columns', _columns);
      await prefs.setDouble('dashboard_layout_spacing', _spacing);
      await prefs.setInt('dashboard_layout_view_mode', _viewMode);

      debugPrint('✅ تم حفظ تفضيلات التخطيط:');
      debugPrint('   الأعمدة: $_columns');
      debugPrint('   التباعد: $_spacing');
      debugPrint('   وضع العرض: $_viewMode (0:شبكي، 1:قائمة، 2:مخصص)');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تفضيلات التخطيط: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildLayoutControls(),
        const SizedBox(height: 16),
        Expanded(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildCurrentLayout(),
          ),
        ),
      ],
    );
  }

  /// بناء عناصر التحكم في التخطيط
  Widget _buildLayoutControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // تبديل وضع العرض
          ToggleButtons(
            isSelected: [
              _viewMode == 0, // شبكي
              _viewMode == 1, // قائمة
              _viewMode == 2, // مخصص
            ],
            onPressed: (index) {
              setState(() {
                _viewMode = index;
              });
              HapticFeedback.selectionClick();
              _saveLayoutPreferences();
            },
            borderRadius: BorderRadius.circular(8),
            children: const [
              Tooltip(
                message: 'عرض شبكي',
                child: Icon(Icons.grid_view),
              ),
              Tooltip(
                message: 'عرض قائمة',
                child: Icon(Icons.view_list),
              ),
              Tooltip(
                message: 'أحجام مخصصة',
                child: Icon(Icons.dashboard_customize),
              ),
            ],
          ),
          
          const SizedBox(width: 16),
          
          // عدد الأعمدة (للعرض الشبكي فقط)
          if (_viewMode == 0) ...[
            Text(
              'الأعمدة:',
              style: AppStyles.bodyMedium,
            ),
            const SizedBox(width: 8),
            DropdownButton<int>(
              value: _columns,
              items: [1, 2, 3, 4].map((columns) {
                return DropdownMenuItem<int>(
                  value: columns,
                  child: Text('$columns'),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _columns = value;
                  });
                  HapticFeedback.selectionClick();
                  _saveLayoutPreferences();
                }
              },
            ),
            
            const SizedBox(width: 16),
          ],
          
          // تباعد البطاقات
          Text(
            'التباعد:',
            style: AppStyles.bodyMedium,
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: Slider(
              value: _spacing,
              min: 8.0,
              max: 32.0,
              divisions: 6,
              onChanged: (value) {
                setState(() {
                  _spacing = value;
                });
              },
              onChangeEnd: (value) {
                HapticFeedback.lightImpact();
                _saveLayoutPreferences();
              },
            ),
          ),
          
          // رسالة توضيحية للوضع المخصص
          if (_viewMode == 2) ...[
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'استخدم وضع التحرير لتغيير الأحجام وإعادة الترتيب',
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const Spacer(),
          
          // وضع التحرير (يتضمن تغيير الأحجام وإعادة الترتيب)
          Tooltip(
            message: _isEditMode ? 'إنهاء التحرير' : 'تحرير البطاقات',
            child: IconButton(
              icon: Icon(
                _isEditMode ? Icons.done : Icons.edit,
                color: _isEditMode ? AppColors.success : AppColors.primary,
              ),
              onPressed: () {
                setState(() {
                  _isEditMode = !_isEditMode;
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ),
          
          // تحديث البيانات
          Tooltip(
            message: 'تحديث البيانات',
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: widget.onRefresh,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التخطيط الحالي حسب الوضع المختار
  Widget _buildCurrentLayout() {
    switch (_viewMode) {
      case 0: // شبكي
        return _buildGridLayout();
      case 1: // قائمة
        return _buildListLayout();
      case 2: // مخصص
        return _buildCustomLayout();
      default:
        return _buildGridLayout();
    }
  }

  /// بناء التخطيط الشبكي
  Widget _buildGridLayout() {
    // استخدام تخطيط موحد لجميع الأوضاع
    return SingleChildScrollView(
      padding: EdgeInsets.all(_spacing),
      child: _buildUnifiedGridLayout(),
    );
  }

  /// بناء تخطيط شبكي موحد يستخدم في جميع الأوضاع
  Widget _buildUnifiedGridLayout() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب العرض المناسب للبطاقة حسب عدد الأعمدة
        final availableWidth = constraints.maxWidth - (_spacing * (_columns - 1));
        final cardWidth = availableWidth / _columns;

        // حساب الارتفاع بناءً على نسبة العرض إلى الارتفاع
        final cardHeight = cardWidth / _calculateAspectRatio();

        // استخدام ReorderableWrap في وضع التحرير، Wrap عادي في الأوضاع الأخرى
        if (_isEditMode) {
          return ReorderableWrap(
            spacing: _spacing,
            runSpacing: _spacing,
            onReorder: _handleReorder,
            children: _buildCardsList(cardWidth, cardHeight),
          );
        } else {
          return Wrap(
            spacing: _spacing,
            runSpacing: _spacing,
            children: _buildCardsList(cardWidth, cardHeight),
          );
        }
      },
    );
  }

  /// بناء قائمة البطاقات بأحجام موحدة
  List<Widget> _buildCardsList(double cardWidth, double cardHeight) {
    return _cards.asMap().entries.map((entry) {
      final index = entry.key;
      final card = entry.value;

      // في وضع التحرير، نسمح للبطاقة بتحديد حجمها بنفسها لإظهار التغييرات الفورية
      if (_isEditMode) {
        return SizedBox(
          key: ValueKey(card.id),
          width: card.size.width.clamp(cardWidth * 0.5, cardWidth * 2.0),
          height: card.size.height.clamp(cardHeight * 0.5, cardHeight * 2.0),
          child: Stack(
            children: [
              _buildCardWithAnimation(card, index),
              // إضافة مقبض السحب في وضع التحرير
              _buildDragHandle(index),
            ],
          ),
        );
      }

      // في الأوضاع الأخرى، نستخدم أحجام ثابتة للتوحيد
      return SizedBox(
        key: ValueKey(card.id),
        width: cardWidth,
        height: cardHeight,
        child: _buildCardWithAnimation(card, index),
      );
    }).toList();
  }

  /// بناء مقبض السحب
  Widget _buildDragHandle(int index) {
    return Positioned(
      top: 8,
      right: 8,
      child: Draggable<int>(
        data: index,
        onDragStarted: () {
          debugPrint('🚀 بدء سحب البطاقة: index=$index');
        },
        onDragEnd: (details) {
          debugPrint('🏁 انتهاء سحب البطاقة: index=$index');
        },
        feedback: Material(
          elevation: 12,
          borderRadius: BorderRadius.circular(12),
          child: Transform.scale(
            scale: 1.2,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.5),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.open_with,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
        childWhenDragging: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue.withValues(alpha: 0.7),
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.open_with,
            color: Colors.white,
            size: 20,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Icon(
            Icons.open_with,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
    );
  }

  /// بناء التخطيط القائمة
  Widget _buildListLayout() {
    return ListView.separated(
      padding: EdgeInsets.all(_spacing),
      itemCount: _cards.length,
      separatorBuilder: (context, index) => SizedBox(height: _spacing),
      itemBuilder: (context, index) {
        return SizedBox(
          height: 300,
          child: _buildCardWithAnimation(_cards[index], index),
        );
      },
    );
  }



  /// بناء البطاقة مع الرسوم المتحركة
  Widget _buildCardWithAnimation(ChartCardModel card, int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300 + (index * 50)),
      curve: Curves.easeOutBack,
      child: ResizableChartCard(
        card: card.copyWith(isResizable: _isEditMode),
        onCardUpdated: widget.onCardUpdated ?? (updatedCard) {
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == updatedCard.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = updatedCard;
            }
          });
        },
        onRefresh: widget.onRefresh,
        onChartTypeChanged: (newType) {
          // تحديث نوع المخطط في القائمة المحلية
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == card.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = _cards[cardIndex].copyWith(chartType: newType);
            }
          });
        },
        onSizeChanged: (newSize) {
          // تحديث حجم البطاقة في القائمة المحلية
          debugPrint('📏 تحديث حجم البطاقة ${card.id}: ${newSize.width}x${newSize.height}');
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == card.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = _cards[cardIndex].copyWith(size: newSize);
              debugPrint('✅ تم تحديث البطاقة في القائمة المحلية');

              // إذا كان في وضع التحرير، نحدث التخطيط فوراً
              if (_isEditMode) {
                // إجبار إعادة بناء التخطيط
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {});
                  }
                });
              }
            } else {
              debugPrint('❌ لم يتم العثور على البطاقة في القائمة');
            }
          });

          // استدعاء callback الخارجي إذا كان متوفراً
          widget.onCardUpdated?.call(_cards.firstWhere((c) => c.id == card.id));

          // حفظ حجم البطاقة في التفضيلات
          _saveCardSize(card.id, newSize);
        },
      ),
    );
  }

  /// بناء التخطيط المخصص (أحجام حرة)
  Widget _buildCustomLayout() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(_spacing),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // في الوضع المخصص، نستخدم أحجام البطاقات المحفوظة مع ضمان التوحيد
          if (_isEditMode) {
            return ReorderableWrap(
              spacing: _spacing,
              runSpacing: _spacing,
              onReorder: _handleReorder,
              children: _buildCustomCardsList(),
            );
          } else {
            return Wrap(
              spacing: _spacing,
              runSpacing: _spacing,
              children: _buildCustomCardsList(),
            );
          }
        },
      ),
    );
  }

  /// بناء قائمة البطاقات للوضع المخصص
  List<Widget> _buildCustomCardsList() {
    return _cards.asMap().entries.map((entry) {
      final index = entry.key;
      final card = entry.value;

      // في وضع التحرير، نسمح للبطاقة بتحديد حجمها بنفسها لإظهار التغييرات الفورية
      if (_isEditMode) {
        return SizedBox(
          key: ValueKey(card.id),
          width: card.size.width.clamp(200.0, double.infinity),
          height: card.size.height.clamp(150.0, double.infinity),
          child: Stack(
            children: [
              _buildCardWithAnimation(card, index),
              // إضافة مقبض السحب في وضع التحرير
              _buildDragHandle(index),
            ],
          ),
        );
      }

      // في الأوضاع الأخرى، نستخدم أحجام البطاقات المحفوظة
      return SizedBox(
        key: ValueKey(card.id),
        width: card.size.width.clamp(200.0, double.infinity),
        height: card.size.height.clamp(150.0, double.infinity),
        child: _buildCardWithAnimation(card, index),
      );
    }).toList();
  }

  /// حساب نسبة العرض إلى الارتفاع
  double _calculateAspectRatio() {
    switch (_columns) {
      case 1:
        return 2.0;
      case 2:
        return 1.3;
      case 3:
        return 1.1;
      case 4:
        return 0.9;
      default:
        return 1.2;
    }
  }





  /// معالجة إعادة الترتيب
  void _handleReorder(int oldIndex, int newIndex) {
    // debugPrint('🔄 بدء إعادة الترتيب:');
    // debugPrint('   oldIndex: $oldIndex');
    // debugPrint('   newIndex (أصلي): $newIndex');

    // التحقق من صحة المؤشرات
    if (oldIndex < 0 || oldIndex >= _cards.length ||
        newIndex < 0 || newIndex >= _cards.length ||
        oldIndex == newIndex) {
      debugPrint('❌ مؤشرات غير صحيحة، إلغاء العملية');
      return;
    }

    setState(() {
      // خوارزمية محسنة لإعادة الترتيب
      final card = _cards.removeAt(oldIndex);
      debugPrint('   البطاقة المزالة: ${card.id}');

      // تعديل المؤشر بناءً على الاتجاه
      int adjustedIndex = newIndex;
      if (newIndex > oldIndex) {
        adjustedIndex = newIndex - 1;
        // debugPrint('   تعديل المؤشر للسحب لليمين: $adjustedIndex');
      } else {
        // debugPrint('   لا حاجة لتعديل المؤشر للسحب لليسار: $adjustedIndex');
      }

      // التأكد من أن المؤشر المعدل صحيح
      adjustedIndex = adjustedIndex.clamp(0, _cards.length);

      // debugPrint('   إدراج البطاقة في الموقع: $adjustedIndex');
      _cards.insert(adjustedIndex, card);

      // debugPrint('   الترتيب الجديد: ${_cards.map((c) => c.id).toList()}');
    });

    widget.onCardsReordered?.call(_cards);
    HapticFeedback.mediumImpact();

    // حفظ الترتيب الجديد في التفضيلات
    _saveLayoutPreferences();

    debugPrint('✅ تم إكمال إعادة الترتيب وحفظ التفضيلات');
  }
}

/// Widget مخصص لإعادة ترتيب العناصر في شكل Wrap
class ReorderableWrap extends StatefulWidget {
  final List<Widget> children;
  final Function(int, int) onReorder;
  final double spacing;
  final double runSpacing;

  const ReorderableWrap({
    super.key,
    required this.children,
    required this.onReorder,
    this.spacing = 0.0,
    this.runSpacing = 0.0,
  });

  @override
  State<ReorderableWrap> createState() => _ReorderableWrapState();
}

class _ReorderableWrapState extends State<ReorderableWrap> {
  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: widget.spacing,
      runSpacing: widget.runSpacing,
      children: widget.children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return DragTarget<int>(
            onWillAcceptWithDetails: (details) {
              final willAccept = details.data != index;
              // debugPrint('🎯 DragTarget[$index]: willAccept=$willAccept, dragData=${details.data}');
              return willAccept;
            },
            onAcceptWithDetails: (details) {
              // debugPrint('✅ DragTarget[$index]: قبول السحب من ${details.data} إلى $index');
              widget.onReorder(details.data, index);
            },
            builder: (context, candidateData, rejectedData) {
              final isHovering = candidateData.isNotEmpty;
              if (isHovering) {
                // debugPrint('🎯 التحويم فوق البطاقة: index=$index, dragData=${candidateData.first}');
              }
              return AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: isHovering
                      ? Border.all(
                          color: Colors.green,
                          width: 3,
                        )
                      : null,
                  // إضافة خلفية خفيفة عند التحويم
                  color: isHovering
                      ? Colors.green.withValues(alpha: 0.1)
                      : null,
                ),
                child: child,
              );
            },
          );
      }).toList(),
    );
  }
}
